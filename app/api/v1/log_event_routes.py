from datetime import datetime
from typing import Optional
from fastapi import API<PERSON><PERSON>er, Depends, Query, status
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.v1 import http_log
from app.controllers.log_event_controller import LogEventController
from app.core.decorators import async_log_and_return_error
from app.models.schemas.job_schema import JobCreate, JobUpdate, Job, JobOutResponse
from app.db.session import get_session
from app.models.schemas.log_event_schema import LogEventCreate, LogEventOut, LogEventOutResponse
from app.utils.conversion import get_date_now_iso, get_utc_dt
from app.utils.enum import EventType, JobStatus, JobType
from app.core.response import Pagination, Response

router = APIRouter(prefix="/logs", tags=["logs"])


# Dependency to get controller
async def get_log_event_controller(session: AsyncSession = Depends(get_session)) -> LogEventController:
    return LogEventController(session)


@router.post("/", status_code=status.HTTP_200_OK)
@async_log_and_return_error(http_log)
async def create_log_event(
    log_event: LogEventCreate, controller: LogEventController = Depends(get_log_event_controller)
):
    """Create a new log event"""
    await controller.create_log_event(log_event)
    return Response()


class GetLogQueryParams(BaseModel):
    station_name: str = Field(description="The station name")
    event: EventType = Field(description="The event type to query", default=EventType.TCPIP)
    from_date: str = Field(
        description="From datetime, YYYY-MM-DD", default=get_date_now_iso()[0], examples=["2024-11-20T00:00:00"]
    )
    to_date: str = Field(
        description="To datetime YYYY-MM-DD", default=get_date_now_iso()[1], examples=["2024-11-22T12:00:00"]
    )
    page: int = Field(description="The page number", default=1)
    per_page: int = Field(description="The number of logs per page", default=100)
    search_string: Optional[str] = Field(description="Search string to filter message content", default=None)

    def to_utc(self):
        return get_utc_dt(datetime.fromisoformat(self.from_date)), get_utc_dt(datetime.fromisoformat(self.to_date))


@router.get("", description="Get the log of the system", response_model=LogEventOutResponse)
@async_log_and_return_error(http_log)
async def get_log(query: GetLogQueryParams, controller: LogEventController = Depends(get_log_event_controller)):
    start, end = query.to_utc()

    if query.search_string:
        logs, total_length = await controller.get_logs_by_content(
            filter_str=query.search_string,
            station_name=query.station_name,
            event=query.event,
            from_date=start,
            to_date=end,
            page=query.page,
            per_page=query.per_page,
        )
    else:
        logs, total_length = await controller.get_logs(
            query.station_name, query.event, start, end, query.page, query.per_page
        )

    logs_pydantic = [LogEventOut.from_sqlmodel(log) for log in logs]
    pagination = Pagination(
        page=query.page,
        per_page=query.per_page,
        total_items=total_length,
    )
    return Response(api_response_model=LogEventOutResponse(data=logs_pydantic, pagination=pagination))
