from typing import List
from sqlalchemy.ext.asyncio import AsyncSession
from dateutil.relativedelta import relativedelta

from app.dal.log_dal import LogDAL, Log


class LogController:

    def __init__(self, session: AsyncSession):
        self.dal = LogDAL(session)
        self.session = session

    async def batch_log(self, logs: List[Log]):
        """Batch insert multiple log entries in a single transaction"""
        if not logs:
            return
        await self.dal.batch_log(logs)

    async def clean_up_log(self, remove_period: relativedelta):
        await self.dal.clean_up_log(remove_period)
