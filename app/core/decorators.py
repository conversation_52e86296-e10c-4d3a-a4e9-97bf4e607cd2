from __future__ import annotations
from http import HTTPStatus
import types, functools
from typing import TYPE_CHECKING, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from .response import Response


if TYPE_CHECKING:
    from app.core.log_manager import LogManager


class BadRequestException(Exception):
    def __init__(self, original_exception):
        super().__init__(f"WMSValidation: {original_exception}")
        self.original_exception = original_exception


def async_log_and_return_error(log_func: LogManager):
    def argument_decorator(original_function):
        @functools.wraps(original_function)
        async def decorated_function(*args, **kwargs):
            try:
                return await original_function(*args, **kwargs)

            except BadRequestException as e:
                detail_error = (
                    f"{original_function.__qualname__}() with postional "
                    f"argument(s) {args}, and keyword argument(s) {kwargs}, raise Exception:{repr(e)}"
                )
                log_func.error(detail_error)
                return Response(
                    status=False, code=HTTPStatus.BAD_REQUEST, message=repr(e.original_exception), error=None
                )
            except Exception as e:
                detail_error = (
                    f"{original_function.__qualname__}() with postional "
                    f"argument(s) {args}, and keyword argument(s) {kwargs}, raise Exception:{repr(e)}"
                )
                log_func.error(detail_error)
                return Response(
                    status=False, code=HTTPStatus.INTERNAL_SERVER_ERROR, message=repr(e), error=detail_error
                )

        return decorated_function

    return argument_decorator


def async_log_and_surpress_error(log_func: LogManager):
    def argument_decorator(original_function):
        @functools.wraps(original_function)
        async def decorated_function(*args, **kwargs):
            try:
                return await original_function(*args, **kwargs)
            except Exception as e:
                log_func.error(f"{original_function.__qualname__}() raise Exception: {e}")

        return decorated_function

    return argument_decorator


def log_and_suppress_error(log_func: LogManager):
    def argument_decorator(original_function):
        @functools.wraps(original_function)
        def decorated_function(*args, **kwargs):
            try:
                return original_function(*args, **kwargs)
            except Exception as e:
                log_func.error(f"{original_function.__qualname__}() raised Exception: {e}")

        return decorated_function

    return argument_decorator


def async_dal_error_handler(suppress_error: bool = True, log_func: Optional["LogManager"] = None):
    """
    Decorator for DAL methods that handles exceptions with session rollback.

    Args:
        suppress_error: If True, suppresses the exception after logging and rollback.
                       If False, re-raises the exception after logging and rollback.
        log_func: Optional LogManager instance for logging. If None, uses print.

    Usage:
        @async_dal_error_handler(suppress_error=True)
        async def some_dal_method(self, ...):
            # DAL method implementation

        @async_dal_error_handler(suppress_error=False, log_func=some_logger)
        async def some_dal_method(self, ...):
            # DAL method implementation that should re-raise errors
    """

    def argument_decorator(original_function):
        @functools.wraps(original_function)
        async def decorated_function(*args, **kwargs):
            try:
                return await original_function(*args, **kwargs)
            except Exception as e:
                # Extract function name and error details
                func_name = original_function.__qualname__
                error_msg = f"Failed to {func_name}: {e}"

                # Log the error
                if log_func:
                    log_func.error(error_msg)
                else:
                    print(error_msg)

                # Attempt session rollback if the first argument has a session attribute
                if args and hasattr(args[0], "session"):
                    session = args[0].session
                    if isinstance(session, AsyncSession):
                        try:
                            await session.rollback()
                        except Exception as rollback_error:
                            rollback_msg = f"Failed to rollback session in {func_name}: {rollback_error}"
                            if log_func:
                                log_func.error(rollback_msg)
                            else:
                                print(rollback_msg)

                # Either suppress or re-raise the exception
                if not suppress_error:
                    raise e

        return decorated_function

    return argument_decorator


def decorateAllFunctionInClass(decorator):
    """
    Decorate all functions with the argument decorator by checking the reflection of iterable obj whether it is equal to types.FunctionType.

    Example Usage:

    @decorateAllFunctionInClass(log_and_suppress_error())
    class YourClass:
        def __init__(self):
            pass

        def functionOne(self):
            print("Function One")

        async def functionTwo(self):
            print("Function Two")

    """

    def decorate(_class):
        for k, v in _class.__dict__.items():
            if isinstance(v, types.FunctionType):  # Regular function
                setattr(_class, k, decorator(v))

            elif isinstance(v, classmethod):  # Class method
                func = v.__func__
                setattr(_class, k, classmethod(decorator(func)))

            elif isinstance(v, staticmethod):  # Static method
                func = v.__func__
                setattr(_class, k, staticmethod(decorator(func)))

            elif isinstance(v, types.CoroutineType):  # Async function
                setattr(_class, k, decorator(v))

        return _class

    return decorate
