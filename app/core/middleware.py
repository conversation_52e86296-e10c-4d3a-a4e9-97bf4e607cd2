import json

from typing import Any, Callable
from fastapi import Request, Response
from fastapi.responses import J<PERSON><PERSON>esponse, StreamingResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from app.core.log_manager import LogManager
from app.core.response import APIResponse, Response as StandardResponse


class ResponseMiddleware(BaseHTTPMiddleware):
    http_log = LogManager("http_log")
    EXCLUDE_PATHS = ["/docs", "/openapi", "/swagger-ui", "/redoc", "/v1/log"]
    EXCLUDE_TOKEN_CHECK = ["/docs", "/openapi", "/auth", "/swagger-ui", "/redoc", "/log"]

    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # TODO add token checking here for login validation
        # TODO extract username from here and add into logging so can track user action
        response = await call_next(request)

        # filter get method
        if request.method == "GET":
            return response

        # filter excluded paths
        if any(path in request.scope["path"] for path in ResponseMiddleware.EXCLUDE_PATHS):
            return response

        res_body = b""
        async for chunk in response.body_iterator:
            res_body += chunk

        ResponseMiddleware.http_log.info(f"Response {request.scope['path']} - {res_body.decode()}")

        return response
