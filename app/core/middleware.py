import json

from typing import Any, Callable
from fastapi import Request, Response
from fastapi.responses import J<PERSON><PERSON>esponse, StreamingResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from app.core.log_manager import LogManager
from app.core.response import APIResponse, Response as StandardResponse, UnauthorizedErrorResponse


class ResponseMiddleware(BaseHTTPMiddleware):
    http_log = LogManager("http_log")
    EXCLUDE_PATHS = ["/docs", "/openapi", "/swagger-ui", "/redoc", "/v1/log"]
    EXCLUDE_TOKEN_CHECK = ["/docs", "/openapi", "/auth", "/swagger-ui", "/redoc", "/log"]
    HEADERS = {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "*",
        "Access-Control-Allow-Headers": "*",
        "Access-Control-Allow-Credentials": "False",  # Match your CORS config
    }

    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # TODO add token checking here for login validation
        # TODO extract username from here and add into logging so can track user action
        from app.auth.token import TokenManager, ExpiredSignatureError, InvalidTokenError

        payload = None

        # filter OPTIONS method for token checking
        if request.method == "OPTIONS":
            response = await call_next(request)
            return response

        # region token checking
        if not any(path in request.scope["path"] for path in self.EXCLUDE_TOKEN_CHECK):
            authorization = request.headers.get("Authorization")

            # if no "authorization" in headers
            if not authorization:
                return StandardResponse(
                    api_response_model=UnauthorizedErrorResponse(message="No authorization token provided"),
                    headers=self.HEADERS,
                )
            try:
                token = authorization.split(" ")[1]
                payload = TokenManager.verify_token(token)
            except ExpiredSignatureError:
                return StandardResponse(
                    api_response_model=UnauthorizedErrorResponse(message="Token has expired"), headers=self.HEADERS
                )
            except Exception:
                return StandardResponse(
                    api_response_model=UnauthorizedErrorResponse(message="Invalid Token Provided"), headers=self.HEADERS
                )

        username = payload.get("username", None) if payload else None

        # endregion

        # filter get method
        if request.method == "GET":
            response = await call_next(request)
            return response

        # filter excluded paths
        if any(path in request.scope["path"] for path in ResponseMiddleware.EXCLUDE_PATHS):
            response = await call_next(request)
            return response

        # log incoming request
        request_body = await request.body()
        try:
            decoded = request_body.decode()
        except UnicodeDecodeError:
            decoded = str()

        ResponseMiddleware.http_log.info(f"{request.scope['method']} {request.scope['path']} - {decoded} - {username}")

        response = await call_next(request)

        res_body = b""
        async for chunk in response.body_iterator:
            res_body += chunk

        ResponseMiddleware.http_log.info(f"Response {request.scope['path']} - {res_body.decode()}")

        # need to use this class instead of straight return because we already iterate through the iterator
        # the content of body is changed
        return Response(
            content=res_body,
            status_code=response.status_code,
            headers=dict(response.headers),
            media_type=response.media_type,
        )
