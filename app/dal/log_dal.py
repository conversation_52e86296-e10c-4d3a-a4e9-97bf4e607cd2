import asyncio

from typing import List

from datetime import datetime
from sqlalchemy.future import select
from sqlalchemy import delete, cast, Date
from sqlalchemy.ext.asyncio import AsyncSession
from dateutil.relativedelta import relativedelta

from app.models.entities.log_model import Log


class LogDAL:

    CHUNK_SIZE = 1000  # Define the chunk size for batch processing

    def __init__(self, session: AsyncSession):
        self.session = session

    async def batch_log(self, logs: List[Log]):
        """Batch insert multiple log entries in a single transaction"""
        try:
            self.session.add_all(logs)
        except Exception as e:
            print(f"Failed to batch log: {e}")
            await self.session.rollback()

    async def clean_up_log(self, remove_period: relativedelta):
        cutoff_date = (datetime.now() - remove_period).date()
        try:
            while True:
                # Select up to chunk_size ids to delete
                result = await self.session.execute(
                    select(Log.id).where(cast(Log.created_at, Date) <= cutoff_date).limit(LogDAL.CHUNK_SIZE)
                )
                ids = result.all()
                if not ids:
                    break
                # Delete this chunk
                await self.session.execute(delete(Log).where(Log.id.in_(ids)))
                await asyncio.sleep(0.1)  # to release context for other coroutine
        except Exception as e:
            print(f"Failed to clean up logs: {e}")
            await self.session.rollback()
