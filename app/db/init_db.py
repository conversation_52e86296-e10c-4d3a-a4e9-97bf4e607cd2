from sqlalchemy import text
from sqlmodel import SQLModel
from sqlalchemy.ext.asyncio import create_async_engine

from app.core.config import CONFIG
from app.utils.console import ConsolePrint

DATABASE_NAME = "micron-be"


async def init_db():
    from app.db.session import engine
    from app.models.entities import log_model, job_model, order_model, setting_model

    master_engine = create_async_engine(CONFIG.DATABASE_URL_MASTER)

    async with master_engine.connect() as conn:
        await conn.execution_options(isolation_level="AUTOCOMMIT")
        result = await conn.execute(text("SELECT 1 FROM sys.databases WHERE name = :dbname"), {"dbname": DATABASE_NAME})
        if result.scalar_one_or_none() is None:
            await conn.execute(text(f"CREATE DATABASE [{DATABASE_NAME}]"))
            ConsolePrint.health(f"✅ Database {DATABASE_NAME} created.")
        else:
            ConsolePrint.health(f"✅ Database {DATABASE_NAME} already exists.")

    async with engine.begin() as conn:
        await conn.run_sync(SQLModel.metadata.create_all)
        ConsolePrint.health("✅ Tables created.")
