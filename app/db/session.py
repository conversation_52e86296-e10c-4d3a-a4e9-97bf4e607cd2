from fastapi import Depends
from typing import AsyncGenerator, Optional, Annotated
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from app.core.config import CONFIG


def create_engine(database_url: Optional[str] = None):
    """Create database engine with optional URL override."""
    url = database_url or CONFIG.DATABASE_URL

    return create_async_engine(
        url,
        echo=False,
        future=True,
        pool_pre_ping=True,
        pool_recycle=300,
        # Connection arguments for MSSQL
        connect_args={
            "timeout": 30,
            "autocommit": True,
        },
        pool_size=20,
        max_overflow=10,
        pool_timeout=30,
    )


# Create default engine
engine = create_engine()


# Create session factory for async sessions
def create_session_factory(engine_instance=None):
    """Create a session factory with optional engine override."""
    target_engine = engine_instance or engine
    return async_sessionmaker(
        target_engine, class_=AsyncSession, expire_on_commit=False, autoflush=False, autocommit=False
    )


# Default session factory
SessionLocal = create_session_factory()


# Dependency for getting a database session
async def get_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency that provides an async database session
    that is automatically closed when the request is finished.

    Uses MSSQL for production.
    """
    async with SessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            print(f"get_session raised exception: {e}")
            # TODO log to file system
            raise
        finally:
            await session.close()


SessionDep = Annotated[AsyncSession, Depends(get_session)]


# Session factory for dependency injection
async def create_db_session(session_factory: Optional[async_sessionmaker] = None) -> AsyncGenerator[AsyncSession, None]:
    """
    Create a database session with optional session factory override.
    This allows for dependency injection scenarios.
    """
    factory = session_factory or SessionLocal
    async with factory() as session:
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            print(f"create_db_session raised exception: {e}")
            # TODO log to file system
            raise
        finally:
            await session.close()
