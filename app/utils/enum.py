from enum import StrEnum


class JobType(StrEnum):
    DEPOSIT = "DEPOSIT"
    WITHDRAW = "WITHDRAW"
    RETURN = "RETURN"


class JobStatus(StrEnum):
    AVAILABLE = "AVAILABLE"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"


class LogLevel(StrEnum):
    I = "INFO"
    W = "WARNING"
    E = "ERROR"


class OrderType(StrEnum):
    DEPOSIT = "DEPOSIT"
    WITHDRAW = "WITHDRAW"
    RETURN = "RETURN"


class OrderStatus(StrEnum):
    AVAILABLE = "AVAILABLE"
    PROCESSING = "PROCESSING"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"
    DELETED = "DELETED"
